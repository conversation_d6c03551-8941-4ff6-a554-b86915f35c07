const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Admin-specific configuration
  env: {
    ADMIN_SUBDOMAIN: 'true',
    PUBLIC_ACCESS: 'false',
    ENHANCED_SECURITY: 'true'
  },

  // Security headers for admin subdomain
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(self), microphone=(self), geolocation=(self), payment=(self)'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://cdn.onesignal.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://js.squareup.com https://api.onesignal.com wss://*.supabase.co; frame-src 'self' https://js.squareup.com;"
          },
          {
            key: 'X-Admin-Portal',
            value: 'true'
          }
        ]
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, private'
          },
          {
            key: 'X-Admin-API',
            value: 'true'
          }
        ]
      }
    ];
  },

  // Admin-specific redirects
  async redirects() {
    return [
      {
        source: '/',
        destination: '/admin/dashboard',
        permanent: false
      },
      {
        source: '/login',
        destination: '/admin/login',
        permanent: false
      },
      // Block public routes
      {
        source: '/shop',
        destination: '/admin/dashboard',
        permanent: false
      },
      {
        source: '/book-online',
        destination: '/admin/bookings',
        permanent: false
      }
    ];
  },

  // Webpack configuration for admin features
  webpack: (config, { isServer }) => {
    // Add path aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname),
      '@/admin': path.resolve(__dirname, 'components/admin'),
      '@/lib': path.resolve(__dirname, 'lib'),
      '@/hooks': path.resolve(__dirname, 'hooks'),
      '@/utils': path.resolve(__dirname, 'utils'),
      '@/types': path.resolve(__dirname, 'types')
    };

    // Admin-specific webpack optimizations
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        util: false,
        crypto: false,
        stream: false,
        buffer: false
      };
    }

    // Canvas support for QR code generation
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push('canvas');
    }

    return config;
  },

  // Image optimization for admin assets
  images: {
    domains: [
      'ndlgbcsbidyhxbpqzgqp.supabase.co',
      'oceansoulsparkles.com.au',
      'admin.oceansoulsparkles.com.au'
    ],
    formats: ['image/webp', 'image/avif']
  },

  // Experimental features for admin performance
  experimental: {
    scrollRestoration: true
  },

  // Admin-specific build configuration
  output: 'standalone',
  
  // Environment-specific settings
  publicRuntimeConfig: {
    adminMode: true,
    enhancedSecurity: true
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['pages', 'components', 'lib', 'hooks', 'utils']
  }
};

module.exports = nextConfig;
