import { createClient } from '@supabase/supabase-js';

// Handle missing environment variables gracefully
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-key';

const supabase = createClient(supabaseUrl, supabaseKey);

export interface AuditLogEntry {
  action: string;
  userId?: string;
  userRole?: string;
  email?: string;
  ip?: string;
  path?: string;
  resource?: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  reason?: string;
  error?: string;
  metadata?: Record<string, any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Log audit event to database and console
 */
export async function auditLog(entry: AuditLogEntry): Promise<void> {
  try {
    const logEntry = {
      action: entry.action,
      user_id: entry.userId,
      user_role: entry.userRole,
      email: entry.email,
      ip_address: entry.ip,
      path: entry.path,
      resource: entry.resource,
      resource_id: entry.resourceId,
      old_values: entry.oldValues,
      new_values: entry.newValues,
      reason: entry.reason,
      error: entry.error,
      metadata: entry.metadata,
      severity: entry.severity || 'medium',
      created_at: new Date().toISOString()
    };

    // Log to database
    const { error } = await supabase
      .from('audit_logs')
      .insert(logEntry);

    if (error) {
      console.error('Failed to write audit log to database:', error);
    }

    // Log to console for immediate visibility
    const logLevel = getLogLevel(entry.severity || 'medium');
    const logMessage = formatLogMessage(entry);
    
    console[logLevel](logMessage);

    // For critical events, also send alerts
    if (entry.severity === 'critical') {
      await sendCriticalAlert(entry);
    }

  } catch (error) {
    console.error('Audit logging failed:', error);
    // Fallback to console logging
    console.error('AUDIT_LOG_FAILURE:', JSON.stringify(entry, null, 2));
  }
}

/**
 * Get appropriate console log level based on severity
 */
function getLogLevel(severity: string): 'log' | 'warn' | 'error' {
  switch (severity) {
    case 'low':
      return 'log';
    case 'medium':
      return 'log';
    case 'high':
      return 'warn';
    case 'critical':
      return 'error';
    default:
      return 'log';
  }
}

/**
 * Format audit log message for console output
 */
function formatLogMessage(entry: AuditLogEntry): string {
  const timestamp = new Date().toISOString();
  const user = entry.userId ? `[User: ${entry.userId}]` : '';
  const ip = entry.ip ? `[IP: ${entry.ip}]` : '';
  const path = entry.path ? `[Path: ${entry.path}]` : '';
  
  return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || ''}`.trim();
}

/**
 * Send critical alert for high-severity events
 */
async function sendCriticalAlert(entry: AuditLogEntry): Promise<void> {
  try {
    // In production, this would send alerts via:
    // - Email to admin team
    // - Slack/Discord webhook
    // - SMS for critical security events
    // - Push notifications
    
    console.error('🚨 CRITICAL SECURITY EVENT:', {
      action: entry.action,
      userId: entry.userId,
      ip: entry.ip,
      reason: entry.reason,
      timestamp: new Date().toISOString()
    });

    // Example: Send email alert (implement based on your email service)
    if (process.env.ENABLE_CRITICAL_ALERTS === 'true') {
      await sendEmailAlert(entry);
    }

  } catch (error) {
    console.error('Failed to send critical alert:', error);
  }
}

/**
 * Send email alert for critical events
 */
async function sendEmailAlert(entry: AuditLogEntry): Promise<void> {
  // Implementation would depend on your email service
  // This is a placeholder for the actual implementation
  console.log('Email alert would be sent for:', entry.action);
}

/**
 * Audit log helper functions for common actions
 */
export const AuditActions = {
  // Authentication events
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILED: 'LOGIN_FAILED',
  LOGIN_BLOCKED: 'LOGIN_BLOCKED',
  LOGOUT: 'LOGOUT',
  MFA_ENABLED: 'MFA_ENABLED',
  MFA_DISABLED: 'MFA_DISABLED',
  MFA_FAILED: 'MFA_FAILED',
  PASSWORD_CHANGED: 'PASSWORD_CHANGED',
  PASSWORD_RESET: 'PASSWORD_RESET',

  // Access control events
  ACCESS_GRANTED: 'ACCESS_GRANTED',
  ACCESS_DENIED: 'ACCESS_DENIED',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  SESSION_TIMEOUT: 'SESSION_TIMEOUT',

  // Data modification events
  RECORD_CREATED: 'RECORD_CREATED',
  RECORD_UPDATED: 'RECORD_UPDATED',
  RECORD_DELETED: 'RECORD_DELETED',
  BULK_UPDATE: 'BULK_UPDATE',
  BULK_DELETE: 'BULK_DELETE',

  // Admin actions
  USER_CREATED: 'USER_CREATED',
  USER_UPDATED: 'USER_UPDATED',
  USER_DEACTIVATED: 'USER_DEACTIVATED',
  ROLE_CHANGED: 'ROLE_CHANGED',
  PERMISSIONS_CHANGED: 'PERMISSIONS_CHANGED',

  // System events
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  CONFIGURATION_CHANGED: 'CONFIGURATION_CHANGED',
  BACKUP_CREATED: 'BACKUP_CREATED',
  BACKUP_RESTORED: 'BACKUP_RESTORED',

  // Security events
  IP_BLOCKED: 'IP_BLOCKED',
  RATE_LIMITED: 'RATE_LIMITED',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  SECURITY_SCAN: 'SECURITY_SCAN',
  VULNERABILITY_DETECTED: 'VULNERABILITY_DETECTED',

  // Business events
  BOOKING_CREATED: 'BOOKING_CREATED',
  BOOKING_MODIFIED: 'BOOKING_MODIFIED',
  BOOKING_CANCELLED: 'BOOKING_CANCELLED',
  PAYMENT_PROCESSED: 'PAYMENT_PROCESSED',
  REFUND_ISSUED: 'REFUND_ISSUED'
};

/**
 * Helper function to log user actions
 */
export async function logUserAction(
  action: string,
  userId: string,
  userRole: string,
  details: Partial<AuditLogEntry> = {}
): Promise<void> {
  await auditLog({
    action,
    userId,
    userRole,
    severity: 'medium',
    ...details
  });
}

/**
 * Helper function to log security events
 */
export async function logSecurityEvent(
  action: string,
  details: Partial<AuditLogEntry> = {}
): Promise<void> {
  await auditLog({
    action,
    severity: 'high',
    ...details
  });
}

/**
 * Helper function to log critical security events
 */
export async function logCriticalEvent(
  action: string,
  details: Partial<AuditLogEntry> = {}
): Promise<void> {
  await auditLog({
    action,
    severity: 'critical',
    ...details
  });
}

/**
 * Helper function to log data changes
 */
export async function logDataChange(
  action: string,
  userId: string,
  resource: string,
  resourceId: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>
): Promise<void> {
  await auditLog({
    action,
    userId,
    resource,
    resourceId,
    oldValues,
    newValues,
    severity: 'medium'
  });
}

/**
 * Get audit logs with filtering and pagination
 */
export async function getAuditLogs(filters: {
  userId?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  severity?: string;
  limit?: number;
  offset?: number;
}) {
  try {
    let query = supabase
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters.userId) {
      query = query.eq('user_id', filters.userId);
    }

    if (filters.action) {
      query = query.eq('action', filters.action);
    }

    if (filters.severity) {
      query = query.eq('severity', filters.severity);
    }

    if (filters.startDate) {
      query = query.gte('created_at', filters.startDate);
    }

    if (filters.endDate) {
      query = query.lte('created_at', filters.endDate);
    }

    if (filters.limit) {
      query = query.limit(filters.limit);
    }

    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return { data: null, error };
  }
}

/**
 * Export audit logs for compliance
 */
export async function exportAuditLogs(
  startDate: string,
  endDate: string,
  format: 'json' | 'csv' = 'json'
) {
  try {
    const { data, error } = await supabase
      .from('audit_logs')
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (format === 'csv') {
      return convertToCSV(data);
    }

    return data;
  } catch (error) {
    console.error('Error exporting audit logs:', error);
    throw error;
  }
}

/**
 * Convert audit logs to CSV format
 */
function convertToCSV(data: any[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        if (typeof value === 'object' && value !== null) {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        }
        return `"${String(value || '').replace(/"/g, '""')}"`;
      }).join(',')
    )
  ].join('\n');

  return csvContent;
}
