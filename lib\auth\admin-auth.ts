import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { createClient } from '@supabase/supabase-js';
import { auditLog } from '../security/audit-logging';

// Handle missing environment variables gracefully
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder';

const supabase = createClient(supabaseUrl, supabaseKey);

export interface AdminUser {
  id: string;
  email: string;
  role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
  firstName: string;
  lastName: string;
  isActive: boolean;
  mfaEnabled: boolean;
  lastActivity: number;
  permissions: string[];
}

export interface AuthResult {
  valid: boolean;
  user?: AdminUser;
  error?: string;
}

export interface LoginResult {
  success: boolean;
  token?: string;
  user?: AdminUser;
  requiresMFA?: boolean;
  mfaSecret?: string;
  error?: string;
}

/**
 * Verify admin authentication token
 */
export async function verifyAdminToken(token: string): Promise<AuthResult> {
  try {
    // Handle missing JWT secret gracefully
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Get user from database with latest info
    const { data: user, error } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        last_activity,
        permissions
      `)
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return { valid: false, error: 'User not found or inactive' };
    }

    // Check if user is still active
    if (!user.is_active) {
      return { valid: false, error: 'User account is deactivated' };
    }

    // Update last activity
    await supabase
      .from('admin_users')
      .update({ last_activity: Date.now() })
      .eq('id', user.id);

    return {
      valid: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        isActive: user.is_active,
        mfaEnabled: user.mfa_enabled,
        lastActivity: Date.now(),
        permissions: user.permissions || []
      }
    };
  } catch (error) {
    return { valid: false, error: 'Invalid token' };
  }
}

/**
 * Admin login with email and password
 */
export async function adminLogin(
  email: string,
  password: string,
  ip?: string
): Promise<LoginResult> {
  try {
    // Check for rate limiting
    const { data: attempts } = await supabase
      .from('login_attempts')
      .select('*')
      .eq('email', email)
      .gte('created_at', new Date(Date.now() - 15 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false });

    if (attempts && attempts.length >= 5) {
      await auditLog({
        action: 'LOGIN_BLOCKED',
        email,
        ip,
        reason: 'Too many failed attempts'
      });
      return { success: false, error: 'Account temporarily locked due to too many failed attempts' };
    }

    // Get user from database
    const { data: user, error } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        password_hash,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `)
      .eq('email', email.toLowerCase())
      .single();



    if (error || !user) {
      await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : 'User not found');
      return { success: false, error: 'Invalid credentials' };
    }

    // Check if user is active
    if (!user.is_active) {
      await auditLog({
        action: 'LOGIN_DENIED',
        userId: user.id,
        email,
        ip,
        reason: 'Account deactivated'
      });
      return { success: false, error: 'Account is deactivated' };
    }

    // Verify password
    const passwordValid = await bcrypt.compare(password, user.password_hash);
    if (!passwordValid) {
      await recordFailedAttempt(email, ip, 'Invalid password');
      return { success: false, error: 'Invalid credentials' };
    }

    // Clear failed attempts on successful password verification
    await supabase
      .from('login_attempts')
      .delete()
      .eq('email', email);

    // Check if MFA is required
    if (user.mfa_enabled && user.mfa_secret) {
      // Return success but indicate MFA is required
      return {
        success: true,
        requiresMFA: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.first_name,
          lastName: user.last_name,
          isActive: user.is_active,
          mfaEnabled: user.mfa_enabled,
          lastActivity: Date.now(),
          permissions: user.permissions || []
        }
      };
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role
      },
      jwtSecret,
      { expiresIn: '8h' }
    );

    // Update last login
    await supabase
      .from('admin_users')
      .update({
        last_login_at: new Date().toISOString(),
        last_activity: Date.now()
      })
      .eq('id', user.id);

    await auditLog({
      action: 'LOGIN_SUCCESS',
      userId: user.id,
      userRole: user.role,
      email,
      ip
    });

    return {
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        isActive: user.is_active,
        mfaEnabled: user.mfa_enabled,
        lastActivity: Date.now(),
        permissions: user.permissions || []
      }
    };
  } catch (error) {
    console.error('Admin login error:', error);
    return { success: false, error: 'Login failed' };
  }
}

/**
 * Verify MFA token and complete login
 * Note: This function requires Node.js runtime due to speakeasy dependency
 */
export async function verifyMFAAndLogin(
  userId: string,
  mfaToken: string,
  ip?: string
): Promise<LoginResult> {
  // Import speakeasy dynamically to avoid Edge Runtime issues
  const speakeasy = await import('speakeasy');

  try {
    const { data: user, error } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `)
      .eq('id', userId)
      .single();

    if (error || !user || !user.mfa_secret) {
      return { success: false, error: 'Invalid MFA setup' };
    }

    // Verify MFA token
    const verified = speakeasy.totp.verify({
      secret: user.mfa_secret,
      encoding: 'base32',
      token: mfaToken,
      window: 2
    });

    if (!verified) {
      await auditLog({
        action: 'MFA_FAILED',
        userId: user.id,
        email: user.email,
        ip,
        reason: 'Invalid MFA token'
      });
      return { success: false, error: 'Invalid MFA token' };
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role
      },
      jwtSecret,
      { expiresIn: '8h' }
    );

    // Update last login
    await supabase
      .from('admin_users')
      .update({
        last_login_at: new Date().toISOString(),
        last_activity: Date.now()
      })
      .eq('id', user.id);

    await auditLog({
      action: 'MFA_LOGIN_SUCCESS',
      userId: user.id,
      userRole: user.role,
      email: user.email,
      ip
    });

    return {
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        isActive: user.is_active,
        mfaEnabled: user.mfa_enabled,
        lastActivity: Date.now(),
        permissions: user.permissions || []
      }
    };
  } catch (error) {
    console.error('MFA verification error:', error);
    return { success: false, error: 'MFA verification failed' };
  }
}

/**
 * Generate MFA secret for user
 * Note: This function requires Node.js runtime due to speakeasy dependency
 */
export async function generateMFASecret(userId: string): Promise<{ secret: string; qrCode: string } | null> {
  // Import speakeasy dynamically to avoid Edge Runtime issues
  const speakeasy = await import('speakeasy');

  try {
    const { data: user } = await supabase
      .from('admin_users')
      .select('email, first_name, last_name')
      .eq('id', userId)
      .single();

    if (!user) return null;

    const secret = speakeasy.generateSecret({
      name: `${user.first_name} ${user.last_name}`,
      issuer: 'Ocean Soul Sparkles Admin',
      length: 32
    });

    return {
      secret: secret.base32,
      qrCode: secret.otpauth_url!
    };
  } catch (error) {
    console.error('MFA secret generation error:', error);
    return null;
  }
}

/**
 * Enable MFA for user
 * Note: This function requires Node.js runtime due to speakeasy dependency
 */
export async function enableMFA(userId: string, secret: string, token: string): Promise<boolean> {
  // Import speakeasy dynamically to avoid Edge Runtime issues
  const speakeasy = await import('speakeasy');

  try {
    // Verify the token first
    const verified = speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2
    });

    if (!verified) return false;

    // Save MFA secret to database
    const { error } = await supabase
      .from('admin_users')
      .update({
        mfa_secret: secret,
        mfa_enabled: true
      })
      .eq('id', userId);

    if (error) return false;

    await auditLog({
      action: 'MFA_ENABLED',
      userId,
      reason: 'User enabled MFA'
    });

    return true;
  } catch (error) {
    console.error('MFA enable error:', error);
    return false;
  }
}

/**
 * Record failed login attempt
 */
async function recordFailedAttempt(email: string, ip?: string, reason?: string) {
  await supabase
    .from('login_attempts')
    .insert({
      email,
      ip_address: ip,
      success: false,
      reason,
      created_at: new Date().toISOString()
    });

  await auditLog({
    action: 'LOGIN_FAILED',
    email,
    ip,
    reason
  });
}

/**
 * Admin logout
 */
export async function adminLogout(userId: string, ip?: string) {
  try {
    await auditLog({
      action: 'LOGOUT',
      userId,
      ip
    });
  } catch (error) {
    console.error('Logout audit error:', error);
  }
}
