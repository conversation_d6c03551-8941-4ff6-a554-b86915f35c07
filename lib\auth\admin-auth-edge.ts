import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';

// <PERSON>le missing environment variables gracefully
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-key';

const supabase = createClient(supabaseUrl, supabaseKey);

export interface AdminUser {
  id: string;
  email: string;
  role: 'DEV' | 'Admin' | 'Artist' | 'Braider';
  firstName: string;
  lastName: string;
  isActive: boolean;
  mfaEnabled: boolean;
  lastActivity: number;
  permissions: string[];
}

export interface AuthResult {
  valid: boolean;
  user?: AdminUser;
  error?: string;
}

/**
 * Verify admin authentication token (Edge Runtime compatible)
 * This version excludes MFA-related functionality to work in Edge Runtime
 */
export async function verifyAdminToken(token: string): Promise<AuthResult> {
  try {
    // Handle missing JWT secret gracefully
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Get user from database with latest info
    const { data: user, error } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        last_activity,
        permissions
      `)
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return { valid: false, error: 'User not found or inactive' };
    }

    // Check if user is still active
    if (!user.is_active) {
      return { valid: false, error: 'User account is deactivated' };
    }

    // Transform database fields to match interface
    const adminUser: AdminUser = {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.first_name,
      lastName: user.last_name,
      isActive: user.is_active,
      mfaEnabled: user.mfa_enabled,
      lastActivity: user.last_activity || Date.now(),
      permissions: user.permissions || []
    };

    return { valid: true, user: adminUser };
  } catch (error) {
    console.error('Token verification error:', error);
    return { valid: false, error: 'Invalid token' };
  }
}

/**
 * Generate JWT token for authenticated user (Edge Runtime compatible)
 */
export function generateAdminToken(user: AdminUser): string {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60) // 8 hours
  };

  const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
  return jwt.sign(payload, jwtSecret);
}

/**
 * Validate JWT token format and expiration (Edge Runtime compatible)
 */
export function validateTokenFormat(token: string): boolean {
  try {
    const jwtSecret = process.env.JWT_SECRET || 'placeholder-secret';
    const decoded = jwt.verify(token, jwtSecret) as any;
    return decoded && decoded.userId && decoded.exp > Math.floor(Date.now() / 1000);
  } catch {
    return false;
  }
}
