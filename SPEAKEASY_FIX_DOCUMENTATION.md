# Speakeasy Edge Runtime Fix Documentation

## Problem
The application was experiencing a runtime error in the browser console:
```
index.tsx:935  Uncaught TypeError: util.deprecate is not a function
    at <unknown> (webpack-internal:///(middleware)/./node_modules/speakeasy/index.js:493)
```

## Root Cause
- Next.js middleware runs in Edge Runtime by default (since Next.js 13)
- The `speakeasy` package uses Node.js-specific utilities like `util.deprecate`
- These utilities are not available in the Edge Runtime environment
- The middleware was importing authentication functions that depended on speakeasy

## Solution
### 1. Separated Authentication Modules
- **`lib/auth/admin-auth-edge.ts`**: Edge Runtime-compatible authentication
  - JWT verification and token generation
  - Basic user authentication without MFA
  - Compatible with middleware execution

- **`lib/auth/admin-auth.ts`**: Full Node.js authentication  
  - Complete MFA functionality using speakeasy
  - Dynamic imports to avoid Edge Runtime issues
  - Used by API routes that run in Node.js runtime

### 2. Updated Middleware
- Changed import from `admin-auth` to `admin-auth-edge`
- Middleware now runs without speakeasy dependencies
- Maintains all security and authentication checks

### 3. Preserved MFA Functionality
- API routes still have full MFA capabilities
- No breaking changes to existing authentication flow
- Speakeasy functions work normally in Node.js runtime

## Files Modified
1. `lib/auth/admin-auth.ts` - Added dynamic speakeasy imports
2. `lib/auth/admin-auth-edge.ts` - New Edge Runtime-compatible module
3. `middleware.ts` - Updated to use Edge Runtime auth
4. `lib/security/audit-logging.ts` - Added graceful env handling

## Testing
The fix has been verified to:
- ✅ Eliminate the speakeasy runtime error
- ✅ Allow middleware to compile and run successfully  
- ✅ Preserve all authentication functionality
- ✅ Maintain MFA capabilities in API routes
- ✅ Handle missing environment variables gracefully

## Usage Guidelines
### For Middleware (Edge Runtime)
```typescript
import { verifyAdminToken } from './lib/auth/admin-auth-edge';
```

### For API Routes (Node.js Runtime)  
```typescript
import { verifyMFAAndLogin, generateMFASecret } from './lib/auth/admin-auth';
```

## Environment Setup
Copy `.env.example` to `.env.local` and configure:
- Supabase URL and service role key
- JWT secret
- Other application settings

## Future Considerations
- Monitor Next.js updates for Edge Runtime changes
- Consider alternative MFA libraries if needed
- Test thoroughly when upgrading Next.js versions
