{"version": 2, "name": "oceansoulsparkles-admin", "alias": ["admin.oceansoulsparkles.com.au"], "regions": ["syd1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "public": false, "env": {"NEXT_PUBLIC_ADMIN_SUBDOMAIN": "true", "NEXT_PUBLIC_SITE_URL": "https://admin.oceansoulsparkles.com.au", "NEXT_PUBLIC_PUBLIC_SITE_URL": "https://www.oceansoulsparkles.com.au", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXTAUTH_URL": "https://admin.oceansoulsparkles.com.au", "NEXTAUTH_SECRET": "@nextauth_secret", "JWT_SECRET": "@jwt_secret", "ENCRYPTION_KEY": "@encryption_key", "MFA_ISSUER": "Ocean Soul Sparkles Admin", "MFA_ENCRYPTION_KEY": "@mfa_encryption_key", "SESSION_TIMEOUT": "3600", "ADMIN_SESSION_TIMEOUT": "1800", "MAX_LOGIN_ATTEMPTS": "5", "LOCKOUT_DURATION": "900", "NEXT_PUBLIC_SQUARE_APPLICATION_ID": "@next_public_square_application_id", "NEXT_PUBLIC_SQUARE_LOCATION_ID": "@next_public_square_location_id", "SQUARE_ACCESS_TOKEN": "@square_access_token", "NEXT_PUBLIC_SQUARE_ENVIRONMENT": "production", "NODE_ENV": "production", "NEXT_PUBLIC_DEBUG_MODE": "false", "NEXT_PUBLIC_ADMIN_ACCESS": "true", "NEXT_PUBLIC_DEV_MODE": "false", "NEXT_PUBLIC_DEBUG_AUTH": "false", "ENABLE_AUTH_BYPASS": "false", "DEV_BYPASS_AUTH": "false", "DEV_MOCK_PAYMENTS": "false", "DEV_DISABLE_MFA": "false", "SMTP_HOST": "smtp.gmail.com", "SMTP_PORT": "587", "SMTP_SECURE": "false", "SMTP_USER": "@smtp_user", "SMTP_PASS": "@smtp_pass", "ADMIN_EMAIL_FROM": "<EMAIL>", "NEXT_PUBLIC_ONESIGNAL_APP_ID": "@next_public_onesignal_app_id", "ONESIGNAL_REST_API_KEY": "@onesignal_rest_api_key", "ONESIGNAL_USER_AUTH_KEY": "@onesignal_user_auth_key", "NEXT_PUBLIC_GOOGLE_ANALYTICS_ID": "@next_public_google_analytics_id", "SENTRY_DSN": "@sentry_dsn", "NEXT_PUBLIC_ENABLE_SECURITY_HEADERS": "true", "NEXT_PUBLIC_DISABLE_CONSOLE_LOGS": "true", "ENABLE_ADMIN_LOGS": "true", "LOG_LEVEL": "info", "BACKUP_ENCRYPTION_KEY": "@backup_encryption_key", "SYNC_INTERVAL": "300000", "ENABLE_AUTO_BACKUP": "true"}, "functions": {"pages/api/**/*.js": {"maxDuration": 30}, "pages/api/auth/**/*.js": {"maxDuration": 10}, "pages/api/admin/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(self), microphone=(self), geolocation=(self), payment=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://connect.squareup.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://connect.squareup.com https://pci-connect.squareup.com https://www.google-analytics.com; frame-src 'self' https://js.squareup.com; object-src 'none'; base-uri 'self'; form-action 'self';"}]}], "redirects": [{"source": "/", "destination": "/admin/dashboard", "permanent": false}, {"source": "/admin", "destination": "/admin/dashboard", "permanent": false}], "rewrites": [{"source": "/api/admin/:path*", "destination": "/api/admin/:path*"}, {"source": "/api/auth/:path*", "destination": "/api/auth/:path*"}], "crons": [{"path": "/api/admin/maintenance/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/admin/backup/auto", "schedule": "0 */6 * * *"}]}