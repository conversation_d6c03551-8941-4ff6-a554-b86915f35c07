import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyAdminToken } from './lib/auth/admin-auth-edge';
import { checkIPRestrictions } from './lib/security/ip-restrictions';
import { rateLimitCheck } from './lib/security/rate-limiting';
import { auditLog } from './lib/security/audit-logging';

// Admin routes that require authentication
const PROTECTED_ROUTES = [
  '/admin/dashboard',
  '/admin/bookings',
  '/admin/customers',
  '/admin/services',
  '/admin/products',
  '/admin/staff',
  '/admin/artists',
  '/admin/reports',
  '/admin/settings',
  '/api/admin'
];

// Public admin routes (login, forgot password, etc.)
const PUBLIC_ADMIN_ROUTES = [
  '/admin/login',
  '/admin/forgot-password',
  '/admin/reset-password',
  '/admin/mfa-setup',
  '/api/auth'
];

// Routes that require specific roles
const ROLE_RESTRICTED_ROUTES = {
  '/admin/staff': ['DEV', 'Admin'],
  '/admin/artists': ['DEV', 'Admin'],
  '/admin/settings': ['DEV', 'Admin'],
  '/admin/reports/financial': ['DEV', 'Admin'],
  '/api/admin/staff': ['DEV', 'Admin'],
  '/api/admin/settings': ['DEV', 'Admin']
};

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();

  // Add security headers to all responses
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-Admin-Portal', 'true');

  try {
    // 1. IP Restrictions Check (if enabled)
    if (process.env.ENABLE_IP_RESTRICTIONS === 'true') {
      const ipCheck = await checkIPRestrictions(request);
      if (!ipCheck.allowed) {
        await auditLog({
          action: 'IP_BLOCKED',
          ip: ipCheck.ip,
          path: pathname,
          reason: 'IP not in whitelist'
        });
        
        return new NextResponse('Access Denied', { status: 403 });
      }
    }

    // 2. Rate Limiting
    const rateLimitResult = await rateLimitCheck(request);
    if (!rateLimitResult.allowed) {
      await auditLog({
        action: 'RATE_LIMITED',
        ip: rateLimitResult.ip,
        path: pathname,
        reason: 'Rate limit exceeded'
      });
      
      return new NextResponse('Too Many Requests', { status: 429 });
    }

    // 3. Check if route requires authentication
    const isProtectedRoute = PROTECTED_ROUTES.some(route => 
      pathname.startsWith(route)
    );
    
    const isPublicAdminRoute = PUBLIC_ADMIN_ROUTES.some(route => 
      pathname.startsWith(route)
    );

    // Allow public admin routes
    if (isPublicAdminRoute) {
      return response;
    }

    // Redirect root to dashboard
    if (pathname === '/') {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url));
    }

    // Check authentication for protected routes
    if (isProtectedRoute) {
      const token = request.cookies.get('admin-token')?.value;
      
      if (!token) {
        await auditLog({
          action: 'UNAUTHORIZED_ACCESS',
          path: pathname,
          reason: 'No authentication token'
        });
        
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }

      // Verify token and get user info
      const authResult = await verifyAdminToken(token);
      
      if (!authResult.valid || !authResult.user) {
        await auditLog({
          action: 'INVALID_TOKEN',
          path: pathname,
          reason: 'Invalid or expired token'
        });
        
        // Clear invalid token
        const loginResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        loginResponse.cookies.delete('admin-token');
        return loginResponse;
      }

      // 4. Role-based access control
      const roleRestriction = Object.entries(ROLE_RESTRICTED_ROUTES).find(([route]) => 
        pathname.startsWith(route)
      );

      if (roleRestriction) {
        const [, allowedRoles] = roleRestriction;
        if (!allowedRoles.includes(authResult.user.role)) {
          await auditLog({
            action: 'INSUFFICIENT_PERMISSIONS',
            userId: authResult.user.id,
            userRole: authResult.user.role,
            path: pathname,
            reason: `Role ${authResult.user.role} not in ${allowedRoles.join(', ')}`
          });
          
          return new NextResponse('Insufficient Permissions', { status: 403 });
        }
      }

      // 5. Session timeout check
      const sessionTimeout = authResult.user.role === 'DEV' ? 
        parseInt(process.env.ADMIN_SESSION_TIMEOUT || '1800') :
        parseInt(process.env.SESSION_TIMEOUT || '3600');

      const tokenAge = Date.now() - authResult.user.lastActivity;
      if (tokenAge > sessionTimeout * 1000) {
        await auditLog({
          action: 'SESSION_TIMEOUT',
          userId: authResult.user.id,
          path: pathname,
          reason: 'Session expired'
        });
        
        const timeoutResponse = NextResponse.redirect(new URL('/admin/login?reason=timeout', request.url));
        timeoutResponse.cookies.delete('admin-token');
        return timeoutResponse;
      }

      // Add user info to headers for API routes
      if (pathname.startsWith('/api/')) {
        response.headers.set('X-User-ID', authResult.user.id);
        response.headers.set('X-User-Role', authResult.user.role);
        response.headers.set('X-User-Email', authResult.user.email);
      }

      // Log successful access
      await auditLog({
        action: 'ACCESS_GRANTED',
        userId: authResult.user.id,
        userRole: authResult.user.role,
        path: pathname,
        ip: request.ip
      });
    }

    // 6. Block access to public routes (redirect to public subdomain)
    const publicRoutes = ['/shop', '/book-online', '/services', '/gallery', '/contact'];
    if (publicRoutes.some(route => pathname.startsWith(route))) {
      const publicUrl = `${process.env.NEXT_PUBLIC_PUBLIC_SITE_URL}${pathname}`;
      return NextResponse.redirect(publicUrl);
    }

    return response;

  } catch (error) {
    console.error('Middleware error:', error);
    
    await auditLog({
      action: 'MIDDLEWARE_ERROR',
      path: pathname,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // In case of error, redirect to login for protected routes
    if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
      return NextResponse.redirect(new URL('/admin/login?error=system', request.url));
    }

    return response;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
